package autoflow

import (
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：Flow自动执行 - Redis内存扩缩容
func TestMemoryScaling(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("memory_scaling")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		paramStr    string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *TaskArgs)
	}{
		{
			name: "unmarshal error",
			args: args{
				clusterName: objC.Name,
				paramStr:    "",
			},
			wantErr: true,
		},
		{
			name: "negative storage size",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"storageSize": -1}`,
			},
			wantErr: true,
		},
		{
			name: "same storage size",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"storageSize": 10}`, // MockCluster默认StorageSize是10GB
			},
			wantErr: true,
		},
		{
			name: "storage size not divisible by shard number",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"storageSize": 5}`, // MockCluster默认ShardNum是2，5不能被2整除
			},
			wantErr: true,
		},
		{
			name: "shard memory size out of range",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"storageSize": 34}`, // MockCluster默认ShardNum是2，34/2=17 > 16GB
			},
			wantErr: true,
		},
		{
			name: "success - memory expansion",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"storageSize": 16}`, // 从10GB扩容到16GB
			},
			wantErr: false,
			expect: func(t *testing.T, args *TaskArgs) {
				if args.Name != "Redis内存扩容" {
					t.Errorf("expect name Redis内存扩容, but got %v", args.Name)
				}
				if len(args.Stages) != 3 {
					t.Errorf("expect 3 stages, but got %d", len(args.Stages))
				}
				if args.Stages[0].Type != omodel.STAGE_TYPE_MIGRATION_DEPLOY {
					t.Errorf("expect stage0.type %s, but got %v", omodel.STAGE_TYPE_MIGRATION_DEPLOY, args.Stages[0].Type)
				}
				if args.Stages[1].Type != omodel.STAGE_TYPE_FAILOVER {
					t.Errorf("expect stage1.type %s, but got %v", omodel.STAGE_TYPE_FAILOVER, args.Stages[1].Type)
				}
				if args.Stages[2].Type != omodel.STAGE_TYPE_MIGRATION_DEPLOY {
					t.Errorf("expect stage2.type %s, but got %v", omodel.STAGE_TYPE_MIGRATION_DEPLOY, args.Stages[2].Type)
				}
				// 检查主从切换阶段是否设置为手动触发
				if args.Stages[1].Automate != false {
					t.Errorf("expect stage1.Automate false, but got %v", args.Stages[1].Automate)
				}
			},
		},
		{
			name: "success - memory reduction",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"storageSize": 4}`, // 从10GB缩容到4GB
			},
			wantErr: false,
			expect: func(t *testing.T, args *TaskArgs) {
				if args.Name != "Redis内存缩容" {
					t.Errorf("expect name Redis内存缩容, but got %v", args.Name)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			args, err := MemoryScaling(tt.args.clusterName, tt.args.paramStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("MemoryScaling() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, args)
			}
		})
	}
}
