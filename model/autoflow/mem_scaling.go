/*
 * Redis内存扩缩容流程控制
 * 流程：从库替换 -> 主从切换 -> 从库替换
 * 说明：
 * 1、Redis内存扩缩容通过修改redis的maxmemory实现，但因为redis进程在pod中，
 *    pod不支持动态调整内存quota，所以需要申请新pod来替换旧pod
 * 2、替换操作只能对从库进行，因为从库不接线上流量
 * 3、主从切换操作因为会对用户产生影响，所以不能设置成自动触发
 */
package autoflow

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
)

// Redis内存扩缩容相关常量
const (
	// 分片内存大小限制（GB）
	MinShardMemorySize = 1
	MaxShardMemorySize = 16

	// 默认主从切换配置
	DefaultMaxConcurrency = 0   // 按IP计算并发
	DefaultIntervalTime   = 120 // 分片完成切换后等待时间（秒）

	// 机房名称
	IDCNameHBB = "hbb"
	IDCNameHBA = "hba"
)

// Redis内存扩缩容流程表单参数
type MemoryScalingSchema struct {
	StorageSize int `json:"storageSize"` // 新的内存大小，单位GB
}

// validateMemoryScalingParams 验证内存扩缩容参数
func validateMemoryScalingParams(clusterName string, params *MemoryScalingSchema, clusterData *ent.Cluster) (int, error) {
	// 检查内存大小是否为正数
	if params.StorageSize <= 0 {
		logger.Error("storage size must be positive, cluster=%s, storageSize=%d", clusterName, params.StorageSize)
		return 0, fmt.Errorf("storage size must be positive")
	}

	// 检查内存大小是否有变化
	if clusterData.StorageSize == params.StorageSize {
		logger.Error("no change in storage size, cluster=%s, current=%d, target=%d", clusterName, clusterData.StorageSize, params.StorageSize)
		return 0, fmt.Errorf("no change in storage size")
	}

	// 检查分片内存配置的合理性
	if params.StorageSize%clusterData.ShardNum != 0 {
		logger.Error("storage size must be divisible by shard number, cluster=%s, storageSize=%d, shardNum=%d", clusterName, params.StorageSize, clusterData.ShardNum)
		return 0, fmt.Errorf("storage size must be divisible by shard number")
	}

	shardMemSize := params.StorageSize / clusterData.ShardNum
	if shardMemSize < MinShardMemorySize || shardMemSize > MaxShardMemorySize {
		logger.Error("shard memory size out of range, cluster=%s, shardMemSize=%d, valid range: %d-%dGB",
			clusterName, shardMemSize, MinShardMemorySize, MaxShardMemorySize)
		return 0, fmt.Errorf("shard memory size out of range, valid range: %d-%dGB", MinShardMemorySize, MaxShardMemorySize)
	}

	return shardMemSize, nil
}

// createMemoryScalingStages 创建内存扩缩容的阶段配置
func createMemoryScalingStages(storageSize, shardMemSize int) []*StageArgs {
	return []*StageArgs{
		{
			Name: "从库替换(第一轮)",
			Type: omodel.STAGE_TYPE_MIGRATION_DEPLOY,
			Parameter: map[string]any{
				"storageSize": storageSize,
				"shardMem":    shardMemSize,
				"phase":       "first",    // 第一轮替换
				"idc":         IDCNameHBB, // 先替换hbb机房的从库
			},
			Automate: true,
		},
		{
			Name: "主从切换",
			Type: omodel.STAGE_TYPE_FAILOVER,
			Parameter: map[string]any{
				"maxConcurnecy": DefaultMaxConcurrency, // 默认按ip计算并发
				"intervalTime":  DefaultIntervalTime,   // 默认在分片完成切换后等待时间
			},
			Automate: false, // 手动触发，避免对用户产生影响
		},
		{
			Name: "从库替换(第二轮)",
			Type: omodel.STAGE_TYPE_MIGRATION_DEPLOY,
			Parameter: map[string]any{
				"storageSize": storageSize,
				"shardMem":    shardMemSize,
				"phase":       "second",   // 第二轮替换
				"idc":         IDCNameHBA, // 再替换hba机房的从库
			},
			Automate: true,
		},
	}
}

// Redis内存扩缩容流程
func MemoryScaling(clusterName string, paramStr string) (*TaskArgs, error) {
	// step1. 解析参数
	var params MemoryScalingSchema
	err := json.Unmarshal([]byte(paramStr), &params)
	if err != nil {
		logger.Warn("failed to unmarshal flow parameter, cluster=%s, parameter=%s, error=(%v)", clusterName, paramStr, err)
		return nil, err
	}

	// step2. 获取集群信息并进行验证
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.Name(clusterName)).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query cluster, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}

	// step3. 参数验证
	shardMemSize, err := validateMemoryScalingParams(clusterName, &params, clusterData)
	if err != nil {
		return nil, err
	}

	// step4. 组装任务参数
	var taskName string
	if clusterData.StorageSize < params.StorageSize {
		taskName = "Redis内存扩容"
	} else {
		taskName = "Redis内存缩容"
	}

	taskArgs := TaskArgs{
		Type: omodel.TASK_TYPE_PROXY_SCALING, // 暂时使用现有类型，后续可以添加专门的内存扩缩容类型
		Name: taskName,
		Stages: createMemoryScalingStages(params.StorageSize, shardMemSize),
	}

	return &taskArgs, nil
}
