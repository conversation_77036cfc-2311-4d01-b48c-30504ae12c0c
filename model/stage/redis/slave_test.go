package redis

import (
	"context"
	"regexp"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-manager/env"
)

// 单测：从库启动
func TestStartSlave(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	// mock data
	objC := env.MockCluster("start_slave")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("start_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("start_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("start_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("start_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())

	type args struct {
		stageId   int64
		bns       string
		instances []*omodel.Instance
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock ragent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`//[\W\w]+/redis/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))

				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("master_link_status:up\nmaster_sync_in_progress:0\nrole:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
			},
			args: args{
				stageId: objS.ID,
				bns:     "redis-test-redis.siod-redis",
				instances: []*omodel.Instance{
					{IP: "************", Port: 7000},
				},
			},
			wantErr: false,
			expect:  func(t *testing.T) {},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StartSlave(tt.args.stageId, tt.args.bns, tt.args.instances)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartSlave() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：从库关停
func TestStopSlave(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	// mock data
	objC := env.MockCluster("stop_slave")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("stop_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("stop_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("stop_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("stop_slave").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())

	// mock redis
	rm := redisc.Mock()

	type args struct {
		stageId   int64
		bns       string
		instances []*omodel.Instance
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock ragent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`//[\W\w]+/redis/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))

				rm.ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				rm.ExpectInfo("Replication").SetVal("role:master\nconnected_slaves:2")
			},
			args: args{
				stageId: objS.ID,
				bns:     "redis-test-redis.siod-redis",
				instances: []*omodel.Instance{
					{IP: "************", Port: 7000},
				},
			},
			wantErr: false,
			expect:  func(t *testing.T) {},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StopSlave(tt.args.stageId, tt.args.bns, tt.args.instances)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSlave() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
