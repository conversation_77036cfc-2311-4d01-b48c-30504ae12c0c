package redis

import (
	"context"
	"fmt"
	"time"

	"dt-common/ent"
	"dt-common/ent/redis"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-manager/library/ragent"
)

// ============================================
//				      启动
// ============================================

// 启动从库
func StartSlave(stageId int64, bns string, instances []*omodel.Instance) error {
	// 数组转map，用来判断是否都遍历到了
	instanceMap := make(map[string]string)
	for _, instance := range instances {
		addr := fmt.Sprintf("%s:%d", instance.IP, instance.Port)
		instanceMap[addr] = ""
	}

	// 从数据库中获取该bns下的所有实例
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to connect mysql, error=(%v)", err)
		return errs.CodeDatabase.Detail("连接数据库失败")
	}
	ctx, cancel := mysql.ContextWithTimeout()
	redisInstances, err := db.Redis.Query().Where(redis.Bns(bns)).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to query redis intances from the database. error=(%v)", err)
		return err
	}

	// 遍历实例，逐个检查
	masterMap := make(map[string]*ent.Redis)
	for _, instance := range redisInstances {
		if instance.Role == omodel.REDIS_ROLE_MASTER {
			masterMap[instance.Name] = instance
		}
		addr := fmt.Sprintf("%s:%d", instance.IP, instance.Port)
		if _, exist := instanceMap[addr]; !exist {
			continue
		}
		instanceMap[addr] = instance.Name
	}

	// 检查参数是否都遍历到了
	for k, v := range instanceMap {
		if v == "" {
			omodel.StageAppendErrorLog(stageId, "The redis instance %s is not in bns %s", k, bns)
			return fmt.Errorf("redis %s is not in bns %s", k, bns)
		}
	}

	// 操作：启动从库
	for _, instance := range instances {
		// 1、调用agent启动slave
		err := ragent.RedisStart(instance.IP, instance.Port)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to start slave %s:%d, error=(%v)", instance.IP, instance.Port, err)
			return err
		}
		omodel.StageAppendErrorLog(stageId, "Succeed to start slave %s:%d, proceed to update metadata", instance.IP, instance.Port)

		// 2、更新状态
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Redis.Update().
			SetStatus(omodel.INSTANCE_STATUS_NORMAL).
			Where(redis.IP(instance.IP), redis.Port(instance.Port)).
			Save(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to update redis status, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to update %s:%d metadata status to normal, proceed to check sync status", instance.IP, instance.Port)

		// 3、检查同步状态，避免rdb打爆master内存，2分钟超时
		shardName := instanceMap[fmt.Sprintf("%s:%d", instance.IP, instance.Port)]
		ctx, cancel = context.WithTimeout(context.Background(), time.Minute*2)
		err = redisc.WaitForSyncComplete(ctx, masterMap[shardName].IP, masterMap[shardName].Port, instance.IP, instance.Port)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, err.Error())
			return err
		}
		omodel.StageAppendErrorLog(stageId, "Slave %s:%d sync finished", instance.IP, instance.Port)
	}
	omodel.StageAppendInfoLog(stageId, "all slaves startup")

	return nil
}

// ============================================
//				      关停
// ============================================

// 从库关停
func StopSlave(stageId int64, bns string, instances []*omodel.Instance) error {
	// 数组转map，用来判断是否都遍历到了
	instanceMap := make(map[string]bool)
	for _, instance := range instances {
		addr := fmt.Sprintf("%s:%d", instance.IP, instance.Port)
		instanceMap[addr] = false
	}

	// 从数据库中获取该bns下的所有实例
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to connect mysql, error=(%v)", err)
		return errs.CodeDatabase.Detail("连接数据库失败")
	}
	ctx, cancel := mysql.ContextWithTimeout()
	redisInstances, err := db.Redis.Query().Where(
		redis.Bns(bns),
		redis.Role(omodel.REDIS_ROLE_SLAVE),
	).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to query redis intances from the database. error=(%v)", err)
		return err
	}

	// 遍历实例，逐个检查
	for _, instance := range redisInstances {
		addr := fmt.Sprintf("%s:%d", instance.IP, instance.Port)
		if _, exist := instanceMap[addr]; !exist {
			continue
		}
		instanceMap[addr] = true
	}

	// 检查参数是否都遍历到了
	for k, v := range instanceMap {
		if !v {
			omodel.StageAppendErrorLog(stageId, "The redis instance %s is not in bns %s", k, bns)
			return fmt.Errorf("redis %s is not in bns %s", k, bns)
		}
	}

	// 操作：关闭从库
	for _, instance := range instances {
		// 1、关停从库
		err = ragent.RedisStop(instance.IP, instance.Port)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to stop slave %s:%d, error=(%v)", instance.IP, instance.Port, err)
			return err
		}
		omodel.StageAppendErrorLog(stageId, "Succeed to stop slave %s:%d, proceed to update metadata", instance.IP, instance.Port)

		// 2、更新状态
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Redis.Update().
			SetStatus(omodel.INSTANCE_STATUS_STOPPED).
			Where(
				redis.IP(instance.IP),
				redis.Port(instance.Port),
			).
			Save(ctx)
		cancel()
		if err != nil {
			// 更新失败人工介入将元数据修改正确
			omodel.StageAppendErrorLog(stageId, "Failed to update redis status, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to update %s:%d metadata status to stopped", instance.IP, instance.Port)
	}
	omodel.StageAppendInfoLog(stageId, "all slaves shutdown")

	return nil
}
