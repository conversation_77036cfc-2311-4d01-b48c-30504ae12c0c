package migration

import (
	"context"
	"fmt"
	"net"
	"os"
	"regexp"
	"syscall"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/proxy"
	"dt-common/ent/redis"
	"dt-common/ent/sentinel"
	"dt-common/mysql"
	"dt-common/redisc"
	"redis-manager/env"
)

// 单测：获取集群实例
func TestGetClusterInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	// mock redis data
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	// mock sentinel data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type args struct {
		StageId     int64
		ClusterName string
		IDC         string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				StageId:     objS.ID,
				ClusterName: "redis_test",
				IDC:         "hba",
			},
			wantErr: false,
		},
		{
			name: "test2",
			args: args{
				StageId:     objS.ID,
				ClusterName: "redis_test",
				IDC:         "hbb",
			},
			wantErr: false,
		},
		{
			name: "test3",
			args: args{
				StageId:     objS.ID,
				ClusterName: "redis_test",
				IDC:         "hbc",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, _, _, err := getClusterInstances(tt.args.StageId, tt.args.ClusterName, tt.args.IDC)
			if (err != nil) != tt.wantErr {
				t.Errorf("getClusterInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 单侧：检查实例存活状态
func TestCheckInstanceAlive(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	mockC := redisc.Mock()

	type args struct {
		stageId      int64
		proxyList    []*ent.Proxy
		redisList    []*ent.Redis
		sentinelList []*ent.Sentinel
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			before: func() {
				mockC.ExpectPing().SetVal("pong")
				mockC.ExpectPing().SetVal("pong")
				mockC.ExpectPing().SetVal("pong")
			},
			args: args{
				stageId:      objS.ID,
				proxyList:    []*ent.Proxy{{IP: "************", Port: 8001}},
				redisList:    []*ent.Redis{{IP: "************", Port: 7000}},
				sentinelList: []*ent.Sentinel{{IP: "************", Port: 9001}},
			},
			wantErr: true,
		},
		{
			name: "test2",
			before: func() {
				mockC.ExpectPing().SetErr(fmt.Errorf("NO AUTH"))
				mockC.ExpectPing().SetVal("pong")
				mockC.ExpectPing().SetVal("pong")
			},
			args: args{
				stageId:      objS.ID,
				proxyList:    []*ent.Proxy{{IP: "************", Port: 8001}},
				redisList:    []*ent.Redis{{IP: "************", Port: 7000}},
				sentinelList: []*ent.Sentinel{{IP: "************", Port: 9001}},
			},
			wantErr: true,
		},
		{
			name: "test3",
			before: func() {
				mockC.ExpectPing().SetErr(&net.OpError{Err: &os.SyscallError{Err: syscall.ECONNREFUSED}})
				mockC.ExpectPing().SetErr(&net.OpError{Err: &os.SyscallError{Err: syscall.ECONNREFUSED}})
				mockC.ExpectPing().SetErr(&net.OpError{Err: &os.SyscallError{Err: syscall.ECONNREFUSED}})
			},
			args: args{
				stageId:      objS.ID,
				proxyList:    []*ent.Proxy{{IP: "************", Port: 8001}},
				redisList:    []*ent.Redis{{IP: "************", Port: 7000}},
				sentinelList: []*ent.Sentinel{{IP: "************", Port: 9001}},
			},
			wantErr: false,
		},
		{
			name: "test4",
			before: func() {
				mockC.ExpectPing().SetErr(fmt.Errorf("connection refuse"))
				mockC.ExpectPing().SetVal("pong")
				mockC.ExpectPing().SetErr(fmt.Errorf("connection refuse"))
			},
			args: args{
				stageId:      objS.ID,
				proxyList:    []*ent.Proxy{{IP: "************", Port: 8001}},
				redisList:    []*ent.Redis{{IP: "************", Port: 7000}},
				sentinelList: []*ent.Sentinel{{IP: "************", Port: 9001}},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt.before()
		t.Run(tt.name, func(t *testing.T) {
			_, _, err := checkInstanceAlive(tt.args.stageId, tt.args.proxyList, tt.args.redisList, tt.args.sentinelList)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkInstanceAlive() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 单侧：物理集群实例下线
func TestBatchDeletedInstance(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	// mock noah api
	httpmock.Reset()
	httpmock.RegisterResponder("GET", `=~^http://noah.duxiaoman-int.com/apptreeNew/v1/products/[\w\W]+/apps/[\w\W]+/instances`,
		httpmock.NewStringResponder(200, `{"success": true, "data": [{"ip":"************","name":"0.redis-test-router.siod-redis","disable":true}]}`))
	httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/instances`),
		httpmock.NewStringResponder(200, `{"success": true, "data": "ok"}`))

	type schema struct {
		stageId int64
		bnsList []string
		ipMap   map[string]int
	}
	tests := []struct {
		name    string
		args    schema
		wantErr bool
	}{
		{
			name: "test1",
			args: schema{
				stageId: objS.ID,
				bnsList: []string{"redis-test-router.siod-redis"},
				ipMap:   map[string]int{"************": 1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := batchDeletedInstance(tt.args.stageId, tt.args.bnsList, tt.args.ipMap)
			if (err != nil) != tt.wantErr {
				t.Errorf("batchDeletedInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDeleteFromDatabase(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	// mock redis data
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	// mock sentinel data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type schema struct {
		stageId     int64
		clusterName string
		idc         string
	}
	tests := []struct {
		name    string
		args    schema
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			args: schema{
				stageId:     objS.ID,
				clusterName: "redis_test",
				idc:         "hba",
			},
			wantErr: false,
			expect: func(t *testing.T) {
				ps, _ := db.Proxy.Query().Where(
					proxy.Idc("hba"),
				).All(context.Background())
				if len(ps) > 0 {
					t.Errorf("deleteFromDatabase() proxy len not 0, current %d", len(ps))
				}

				rs, _ := db.Redis.Query().Where(
					redis.Idc("hba"),
				).All(context.Background())
				if len(rs) > 0 {
					t.Errorf("deleteFromDatabase() redis len not 0, current %d", len(rs))
				}

				ss, _ := db.Sentinel.Query().Where(
					sentinel.Idc("hba"),
				).All(context.Background())
				if len(ss) > 0 {
					t.Errorf("deleteFromDatabase() sentinel len not 0, current %d", len(ss))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := deleteFromDatabase(tt.args.stageId, tt.args.clusterName, tt.args.idc)
			if (err != nil) != tt.wantErr {
				t.Errorf("deleteFromDatabase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})

		tt.expect(t)
	}
}
