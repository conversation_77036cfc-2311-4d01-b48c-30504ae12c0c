package migration

import (
	"context"
	"fmt"
	"net"
	"os"
	"strings"
	"syscall"

	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/proxy"
	"dt-common/ent/redis"
	"dt-common/ent/sentinel"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"dt-common/redisc"
)

// 判断是否是connection refused错误
func isConnRefusedError(err error) bool {
	opErr, ok := err.(*net.OpError)
	if !ok {
		return false
	}
	sysErr, ok := opErr.Err.(*os.SyscallError)
	if !ok {
		return false
	}
	if sysErr.Err != syscall.ECONNREFUSED {
		return false
	}

	return true
}

// 获取集群实例列表
func getClusterInstances(stageId int64, clusterName string, idc string) ([]*ent.Proxy, []*ent.Redis, []*ent.Sentinel, error) {
	var (
		err          error
		proxyList    []*ent.Proxy
		redisList    []*ent.Redis
		sentinelList []*ent.Sentinel
	)

	db, err := mysql.Database()
	if err != nil {
		return nil, nil, nil, errs.CodeDatabase.Detail("查询数据库出错，请稍后刷新重试")
	}

	g, _ := errgroup.WithContext(context.Background())
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		proxyList, err = db.Proxy.Query().Where(
			proxy.ClusterName(clusterName),
			proxy.Idc(idc),
			proxy.Docker(omodel.DEPLOY_ENV_BBC),
		).All(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		redisList, err = db.Redis.Query().Where(
			redis.ClusterName(clusterName),
			redis.Idc(idc),
			redis.Docker(omodel.DEPLOY_ENV_BBC),
		).All(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		sentinelList, err = db.Sentinel.Query().Where(
			sentinel.ClusterName(clusterName),
			sentinel.Idc(idc),
			sentinel.Docker(omodel.DEPLOY_ENV_BBC),
		).All(ctx)
		cancel()
		return err
	})
	if err = g.Wait(); err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to query instance, cluster=%s, idc=%s, error=(%v)", clusterName, idc, err)
		return nil, nil, nil, err
	}

	if len(proxyList) == 0 && len(redisList) == 0 && len(sentinelList) == 0 {
		omodel.StageAppendWarnLog(stageId, "Find no instance with clusterName=%s, idc=%s", clusterName, idc)
		return nil, nil, nil, errs.CodeDatabase.Detail("Find no instance with clusterName=%s, idc=%s", clusterName, idc)
	}

	return proxyList, redisList, sentinelList, nil
}

// 检查实例存活状态
func checkInstanceAlive(stageId int64, proxyList []*ent.Proxy, redisList []*ent.Redis, sentinelList []*ent.Sentinel) ([]string, map[string]int, error) {
	var proxyBns, redisBns, sentinelBns string
	ipMap := make(map[string]int)

	g, _ := errgroup.WithContext(context.Background())
	g.Go(func() error {
		for _, item := range proxyList {
			rc, err := redisc.Client(item.IP, item.Port)
			if err != nil {
				omodel.StageAppendErrorLog(stageId, "Can not get redis connection, ip=%s, port=%d", item.IP, item.Port)
				return err
			}

			ctx, cancel := redisc.ReadTimeout()
			err = rc.Ping(ctx).Err()
			cancel()
			if err == nil || !isConnRefusedError(err) {
				err = fmt.Errorf("proxy instance %s:%d has not been shut down", item.IP, item.Port)
				omodel.StageAppendErrorLog(stageId, err.Error())
				return err
			}
			proxyBns = item.Bns
			ipMap[item.IP] = 1
			omodel.StageAppendInfoLog(stageId, "proxy instance %s:%d has been shut down", item.IP, item.Port)
		}
		omodel.StageAppendInfoLog(stageId, "All proxy has been shutdown")
		return nil
	})
	g.Go(func() error {
		for _, item := range redisList {
			rc, err := redisc.Client(item.IP, item.Port)
			if err != nil {
				omodel.StageAppendErrorLog(stageId, "Can not get redis connection, ip=%s, port=%d", item.IP, item.Port)
				return err
			}

			ctx, cancel := redisc.ReadTimeout()
			err = rc.Ping(ctx).Err()
			cancel()
			if err == nil || !isConnRefusedError(err) {
				err = fmt.Errorf("redis instance %s:%d has not been shut down", item.IP, item.Port)
				omodel.StageAppendErrorLog(stageId, err.Error())
				return err
			}
			redisBns = item.Bns
			ipMap[item.IP] = 1
			omodel.StageAppendInfoLog(stageId, "redis instance %s:%d has been shut down", item.IP, item.Port)
		}
		omodel.StageAppendInfoLog(stageId, "All redis has been shutdown")
		return nil
	})
	g.Go(func() error {
		for _, item := range sentinelList {
			rc, err := redisc.Client(item.IP, item.Port)
			if err != nil {
				omodel.StageAppendErrorLog(stageId, "Can not get redis connection, ip=%s, port=%d", item.IP, item.Port)
				return err
			}

			ctx, cancel := redisc.ReadTimeout()
			err = rc.Ping(ctx).Err()
			cancel()
			if err == nil || !isConnRefusedError(err) {
				err = fmt.Errorf("sentinel instance %s:%d has not been shut down", item.IP, item.Port)
				omodel.StageAppendErrorLog(stageId, err.Error())
				return err
			}
			sentinelBns = item.Bns
			ipMap[item.IP] = 1
			omodel.StageAppendInfoLog(stageId, "sentinel instance %s:%d has been shut down", item.IP, item.Port)
		}
		omodel.StageAppendInfoLog(stageId, "All sentinel has been shutdown")
		return nil
	})
	if err := g.Wait(); err != nil {
		return nil, nil, err
	}

	return []string{proxyBns, redisBns, sentinelBns}, ipMap, nil
}

// 删除bns下实例
func batchDeletedInstance(stageId int64, bnsList []string, ipMap map[string]int) error {
	for _, bns := range bnsList {
		result := strings.Split(bns, ".")
		appName := result[0]
		product := result[1]

		instances, err := noah.GetInstances(product, appName)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to get instance, product=%s, app=%s", product, appName)
			return err
		}

		var hostNames []string
		for _, instance := range instances {
			if _, exist := ipMap[instance.IP]; !exist {
				continue
			}
			// 实例没屏蔽，不能执行删除
			if !instance.Disable && strings.Contains(bns, "-router") {
				errmsg := fmt.Sprintf("Instance %s is not blocked, can not proceed to delete, bns=%s", instance.IP, bns)
				omodel.StageAppendErrorLog(stageId, errmsg)
				return fmt.Errorf(errmsg)
			}
			hostNames = append(hostNames, instance.Name)
		}
		if len(hostNames) == 0 {
			omodel.StageAppendInfoLog(stageId, "there are no matching instances")
			return nil
		}
		omodel.StageAppendInfoLog(stageId, "Instance is going to delete, instances=%+v", hostNames)
		err = noah.RemoveInstances(product, appName, hostNames)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to delete instance, product=%s, app=%s, hostNames=%+v", product, appName, hostNames)
			return err
		}
	}

	return nil
}

// 删除数据库中的proxy、redis和sentinel数据
func deleteFromDatabase(stageId int64, clusterName string, idc string) error {
	db, err := mysql.Database()
	if err != nil {
		return errs.CodeDatabase.Detail("查询数据库出错，请稍后刷新重试")
	}

	g, _ := errgroup.WithContext(context.Background())
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Proxy.Delete().
			Where(
				proxy.ClusterName(clusterName),
				proxy.Idc(idc),
				proxy.Docker(omodel.DEPLOY_ENV_BBC),
			).
			Exec(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to delete proxy metadata , error=(%v)", err)
			return errs.CodeRequestFailed.Detail("proxy元数据删除失败")
		}
		return nil
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Redis.Delete().
			Where(
				redis.ClusterName(clusterName),
				redis.Idc(idc),
				redis.Docker(omodel.DEPLOY_ENV_BBC)).
			Exec(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to delete redis metadata , error=(%v)", err)
			return errs.CodeRequestFailed.Detail("redis元数据删除失败")
		}
		return nil
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Sentinel.Delete().
			Where(
				sentinel.ClusterName(clusterName),
				sentinel.Idc(idc),
				sentinel.Docker(omodel.DEPLOY_ENV_BBC),
			).
			Exec(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to delete sentinel metadata , error=(%s)", err)
			return errs.CodeRequestFailed.Detail("sentinel元数据删除失败")
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		return err
	}
	return nil
}

type StageSchemaIdc struct {
	IDC string `json:"idc" binding:"required"`
}

// 实例下线，根据参数下线对应机房的失效实例
// 1、从BNS中摘除实例
// 2、从数据库中清除记录
func InstanceUnmount(stageId int64, idc string) error {
	stageData, err := omodel.GetStageDetail(stageId)
	if err != nil {
		return err
	}

	// 2、获取集群实例信息
	proxyList, redisList, sentinelList, err := getClusterInstances(stageData.ID, stageData.ClusterName, idc)
	if err != nil {
		return errs.CodeDatabase.Detail("实例信息查询失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "Succeed to get instance, idc=%s, proxy=%d, redis=%d, sentinel=%d", idc, len(proxyList), len(redisList), len(sentinelList))

	// 3、检查待下线实例是否已经关停
	bnsList, ipMap, err := checkInstanceAlive(stageData.ID, proxyList, redisList, sentinelList)
	if err != nil {
		return errs.CodeRequestFailed.Detail("实例存活状态检查失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "All instance is shutdown, proceed to delete from bns")

	// 4、对Bns的下的实例进行删除
	err = batchDeletedInstance(stageData.ID, bnsList, ipMap)
	if err != nil {
		return errs.CodeRequestFailed.Detail("删除实例失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "Succeed to delete instance from bns, proceed to delete from database")

	// 5、删除数据库元数据
	err = deleteFromDatabase(stageData.ID, stageData.ClusterName, idc)
	if err != nil {
		return errs.CodeDatabase.Detail("删除数据库失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "Succeed to delete metadata")

	return nil
}
