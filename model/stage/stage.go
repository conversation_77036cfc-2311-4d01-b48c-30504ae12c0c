package stage

import (
	"fmt"
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/xweb"
	"redis-manager/library/errc"
)

// 后台执行时，panic不会被gin.Recovery捕获
func AsyncRecovery(params *xweb.CallBackParam) {
	if err := recover(); err != nil {
		params.Success = false
		params.Message = fmt.Sprintf("%v", err)
	}
	xweb.StageCallBack(params)
}

// 后台执行变更函数，结束回调xweb
func Async(stageId int64, syncFun interface{}, args ...reflect.Value) error {
	// 判断syncFunc类型是否符合要求
	ft := reflect.TypeOf(syncFun)
	if ft.Kind() != reflect.Func || ft.NumOut() != 1 {
		return errc.CodeUndefinedValue.Detail("syncFunc should be a function with an error return value")
	}

	// 遇到单测退出
	if gin.Mode() == gin.TestMode {
		return nil
	}

	// 后台执行变更逻辑，defer callback
	go func() {
		cbParams := xweb.CallBackParam{
			StageID: stageId,
		}
		defer AsyncRecovery(&cbParams)

		f := reflect.ValueOf(syncFun)
		result := f.Call(args)
		// return nil表示执行成功，否则表示执行失败
		if result[0].IsNil() {
			cbParams.Success = true
			return
		}

		// 执行失败后上报错误信息给xweb
		err := result[0].Interface().(error)
		if err != nil {
			cbParams.Success = false
			cbParams.Message = err.Error()
		}
	}()

	return nil
}
