package whitelist

import (
	"context"
	"regexp"
	"testing"

	"dt-common/ent"
	"dt-common/mysql"
	"redis-manager/env"

	"github.com/jarcoal/httpmock"
)

// 单测：获取集群实例
func TestGetClusterInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	// mock redis data
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	// mock sentinel data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type args struct {
		ClusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*ent.Proxy, []*ent.Redis, []*ent.Sentinel)
	}{
		{
			name: "test1",
			args: args{
				ClusterName: "redis_test",
			},
			wantErr: false,
			expect: func(t *testing.T, p []*ent.Proxy, r []*ent.Redis, s []*ent.Sentinel) {
				if len(p) != 2 {
					t.Errorf("proxy len = %d, want 2", len(p))
				}
				if len(r) != 4 {
					t.Errorf("redis len = %d, want 4", len(r))
				}
				if len(s) != 3 {
					t.Errorf("sentinel len = %d, want 3", len(s))
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			proxyList, redisList, sentinelList, err := getInstanceByClusterName(tt.args.ClusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("getInstanceByClusterName() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, proxyList, redisList, sentinelList)
			}
		})
	}
}

// 单测：获取集群实例
func TestUpdateManageWhitelist(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	// mock redis data
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	// mock sentinel data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId     int64
		clusterName string
		action      string
		bnsList     []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"page\":{\"totalCount\":0}, \"data\": []}"))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "add",
				bnsList:     []string{"jiayiming.bns"},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/update"] != 9 {
					t.Errorf("expect call 9 but %d", info["POST =~://[\\w\\W]+/whitelist/update"])
				}
				httpmock.Reset()
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateManageWhitelist(tt.args.stageId, tt.args.clusterName, tt.args.action, tt.args.bnsList)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateManageWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
