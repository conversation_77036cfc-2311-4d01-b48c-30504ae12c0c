package whitelist

import (
	"fmt"
	"net"
	"strings"

	"dt-common/ent/proxy"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"redis-manager/library/ragent"
)

// 检查业务IP白名单的格式、IP合法性、权限是否错误
// 格式：xx.xx.xx.xxx r/rw
func checkIPWhiteList(input []string) error {
	for i := 0; i < len(input); i++ {
		parts := strings.Split(input[i], " ")
		if len(parts) != 2 {
			return fmt.Errorf("ip whitelist format error=(%s)", input[i])
		}
		if net.ParseIP(parts[0]) == nil {
			return fmt.Errorf("ip whitelist address error=(%s)", input[i])
		}
		if parts[1] != "r" && parts[1] != "rw" {
			return fmt.Errorf("ip whitelist permission error=(%s)", input[i])
		}
	}
	return nil
}

// 检查业务BNS白名单的格式、BNS合法性、权限是否错误
// 格式：xxxxx.xxxx r/rw
func checkBNSWhiteList(action string, input []string) error {
	for i := 0; i < len(input); i++ {
		parts := strings.Split(input[i], " ")
		if len(parts) != 2 {
			return fmt.Errorf("bns whitelist format error=(%s)", input[i])
		}
		if action == "add" {
			_, err := noah.GetInstancesByBns(parts[0])
			if err != nil {
				return fmt.Errorf("bns whitelist address error=(%s)", input[i])
			}
		}
		if parts[1] != "r" && parts[1] != "rw" {
			return fmt.Errorf("bns whitelist permission error=(%s)", input[i])
		}
	}
	return nil
}

// 更新业务白名单，仅对proxy变更
func UpdateProxyWhitelist(stageId int64, clusterName string, action string, bnsList []string, ipList []string) error {
	// 检查BNS白名单正确性
	err := checkBNSWhiteList(action, bnsList)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to check bns whitelist, error=(%v)", err)
		return errs.CodeRequestParameter.Detail("BNS白名单错误")
	}

	// 检查IP白名单的格式
	err = checkIPWhiteList(ipList)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to check ip whitelist, error=(%v)", err)
		return errs.CodeRequestParameter.Detail("IP白名单错误")
	}

	// 获取集群proxy列表
	db, err := mysql.Database()
	if err != nil {
		return err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	proxyList, err := db.Proxy.Query().Where(proxy.ClusterName(clusterName), proxy.Docker(omodel.DEPLOY_ENV_BBC)).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to query proxy instance, error=(%v)", err)
		return errs.CodeDatabase.Detail("查询proxy出错，请稍后刷新重试")
	}
	omodel.StageAppendInfoLog(stageId, "succeed to query proxy instance, proceed to call redis-agent to update whitelist")

	// 遍历proxy实例更新白名单
	for _, item := range proxyList {
		err := ragent.UpdateWhitelist(item.IP, &ragent.UpdateParams{
			Port:    item.Port,
			Action:  action,
			BnsList: bnsList,
			IpList:  ipList,
		})
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "failed to call redis-agent to %s proxy %s:%d whitelist, error=(%v)", action, item.IP, item.Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "succeed to %s proxy %s:%d whitelist,", action, item.IP, item.Port)
	}

	omodel.StageAppendInfoLog(stageId, "all proxies are updated")

	return nil
}
