package whitelist

import (
	"context"

	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/proxy"
	"dt-common/ent/redis"
	"dt-common/ent/sentinel"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"redis-manager/library/ragent"
)

// 根据clustername来查询并返回集群的proxy、redis、sentinel信息
func getInstanceByClusterName(clusterName string) ([]*ent.Proxy, []*ent.Redis, []*ent.Sentinel, error) {
	//连接数据库
	db, err := mysql.Database()
	if err != nil {
		return nil, nil, nil, err
	}

	var proxyList []*ent.Proxy
	var redisList []*ent.Redis
	var sentinelList []*ent.Sentinel
	g, _ := errgroup.WithContext(context.Background())
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		proxyList, err = db.Proxy.Query().Where(
			proxy.ClusterName(clusterName),
			proxy.Docker(omodel.DEPLOY_ENV_BBC),
		).All(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		redisList, err = db.Redis.Query().Where(
			redis.ClusterName(clusterName),
			redis.Docker(omodel.DEPLOY_ENV_BBC),
		).All(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		sentinelList, err = db.Sentinel.Query().Where(
			sentinel.ClusterName(clusterName),
			sentinel.Docker(omodel.DEPLOY_ENV_BBC),
		).All(ctx)
		cancel()
		return err
	})
	if err = g.Wait(); err != nil {
		return nil, nil, nil, err
	}

	return proxyList, redisList, sentinelList, nil
}

// 调用agent更新proxy bns白名单
func updateProxy(stageId int64, proxyList []*ent.Proxy, preBnsList []string, action string, privilege string) error {
	bnsList := make([]string, len(preBnsList))
	for i := range preBnsList {
		bnsList[i] = preBnsList[i] + " " + privilege
	}
	// 调用每个proxy的agent修改管理白名单，agent修改白名单逻辑为先备份再增加
	for i := range proxyList {
		data := ragent.UpdateParams{
			Port:    proxyList[i].Port,
			BnsList: bnsList,
			IpList:  []string{},
			Action:  action,
		}
		err := ragent.UpdateWhitelist(proxyList[i].IP, &data)
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "Failed to call redis-agent to update proxy whitelist, stageId=%d, proxy=%s:%d, error=(%v)", stageId, proxyList[i].IP, proxyList[i].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to call redis-agent to update proxy whitelist, proxy=%s:%d", proxyList[i].IP, proxyList[i].Port)
	}

	return nil
}

// 调用agent更新redis bns白名单
func updateRedis(stageId int64, redisList []*ent.Redis, preBnsList []string, action string) error {
	bnsList := make([]string, len(preBnsList))
	for i := range preBnsList {
		bnsList[i] = preBnsList[i] + " rw"
	}
	// 调用每个redis的agent修改管理白名单，agent修改白名单逻辑为先备份再增加
	for i := range redisList {
		data := ragent.UpdateParams{
			Port:    redisList[i].Port,
			BnsList: bnsList,
			IpList:  []string{},
			Action:  action,
		}
		err := ragent.UpdateWhitelist(redisList[i].IP, &data)
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "Failed to call redis-agent to update redis whitelist, stageId=%d, redis=%s:%d, error=(%v)", stageId, redisList[i].IP, redisList[i].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to call redis-agent to update redis whitelist, redis=%s:%d", redisList[i].IP, redisList[i].Port)
	}

	return nil
}

// 调用agent更新sentinel bns白名单
func updateSentinel(stageId int64, sentinelList []*ent.Sentinel, preBnsList []string, action string) error {
	bnsList := make([]string, len(preBnsList))
	for i := range preBnsList {
		bnsList[i] = preBnsList[i] + " x"
	}
	// 调用每个sentinel的agent修改管理白名单，agent修改白名单逻辑为先备份再增加
	for i := range sentinelList {
		data := ragent.UpdateParams{
			Port:    sentinelList[i].Port,
			BnsList: bnsList,
			IpList:  []string{},
			Action:  action,
		}
		err := ragent.UpdateWhitelist(sentinelList[i].IP, &data)
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "Failed to call redis-agent to update sentinel whitelist, stageId=%d, sentinel=%s:%d, error=(%v)", stageId, sentinelList[i].IP, sentinelList[i].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to call redis-agent to update sentinel whitelist, sentinel=%s:%d", sentinelList[i].IP, sentinelList[i].Port)
	}

	return nil
}

// 检查管理BNS白名单的BNS合法性
func checkBNS(input []string) error {
	for _, bns := range input {
		_, err := noah.GetInstancesByBns(bns)
		if err != nil {
			return err
		}
	}
	return nil
}

type StageSchemaWhitelist struct {
	BnsList []string `json:"bnsList"`
	Action  string   `json:"action"`
}

// 更新管理白名单（proxy、redis、sentinel）
func UpdateManageWhitelist(stageId int64, clusterName string, action string, bnsList []string) error {
	if action == "add" {
		// 1、检查白名单是否存在
		err := checkBNS(bnsList)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "Failed to check bns whitelist, error=(%v)", err)
			return errs.CodeRequestParameter.Detail("白名单异常")
		}
	}
	omodel.StageAppendInfoLog(stageId, "Succeed to check bns, proceed to get instance from database")

	// 2、查询集群元数据信息
	proxyList, redisList, sentinelList, err := getInstanceByClusterName(clusterName)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to query instance, error=(%v)", err)
		return errs.CodeDatabase
	}
	omodel.StageAppendInfoLog(stageId, "Succeed to get instance, proceed to call agent to update")

	// 3、请求agent添加白名单
	// 3.1 proxy
	err = updateProxy(stageId, proxyList, bnsList, action, "rwx")
	if err != nil {
		return errs.CodeRequestFailed.Detail("更新管理白名单失败")
	}
	omodel.StageAppendInfoLog(stageId, "All proxy whitelist has been updated")
	// 3.2 redis
	err = updateRedis(stageId, redisList, bnsList, action)
	if err != nil {
		return errs.CodeRequestFailed.Detail("更新管理白名单失败")
	}
	omodel.StageAppendInfoLog(stageId, "All redis whitelist has been updated")
	// 3.3 sentinel
	err = updateSentinel(stageId, sentinelList, bnsList, action)
	if err != nil {
		return errs.CodeRequestFailed.Detail("更新管理白名单失败")
	}
	omodel.StageAppendInfoLog(stageId, "All sentinel whitelist has been updated")

	return nil
}
