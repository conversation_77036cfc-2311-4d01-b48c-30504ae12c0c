package whitelist

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"redis-manager/env"
)

// 单侧：调agent回滚管理白名单
func TestRollbackProxyWhitelist(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId     int64
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/rollback`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/rollback"] != 2 {
					t.Errorf("expect call 2 but %d", info["POST =~://[\\w\\W]+/whitelist/rollback"])
				}
				httpmock.Reset()
			},
		},
		{
			name: "test2",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/rollback`),
					httpmock.NewStringResponder(200, `{"code": "S0001", "msg": "mock not ok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/rollback"] != 1 {
					t.Errorf("expect call 2 but %d", info["POST =~://[\\w\\W]+/whitelist/rollback"])
				}
				httpmock.ZeroCallCounters()
			},
		},
	}

	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			if err := RollbackProxyWhitelist(tt.args.stageId, tt.args.clusterName); (err != nil) != tt.wantErr {
				t.Errorf("RollbackProxyWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单侧：调agent回滚管理白名单
func TestRollbackManageWhitelist(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	// mock redis data
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	// mock sentinel data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(0).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("redis_test").SetDocker(1).SetBns("redis-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId     int64
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/rollback`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/rollback"] != 9 {
					t.Errorf("expect call 9 but %d", info["POST =~://[\\w\\W]+/whitelist/rollback"])
				}
				httpmock.Reset()
			},
		},
		{
			name: "test2",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/rollback`),
					httpmock.NewStringResponder(200, `{"code": "S0001", "msg": "mock not ok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/rollback"] != 1 {
					t.Errorf("expect call 2 but %d", info["POST =~://[\\w\\W]+/whitelist/rollback"])
				}
				httpmock.ZeroCallCounters()
			},
		},
	}

	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			if err := RollbackManageWhitelist(tt.args.stageId, tt.args.clusterName); (err != nil) != tt.wantErr {
				t.Errorf("RollbackManageWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
