package whitelist

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"redis-manager/env"
)

// 单侧：ip白名单校验
func TestCheckIPWhiteList(t *testing.T) {
	type args struct {
		ipwhitelist []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "test1",
			args:    args{ipwhitelist: []string{"************ rw", "************ rw"}},
			wantErr: false,
		},
		{
			name:    "test2",
			args:    args{ipwhitelist: []string{"10.32.162.256 rw", "************ rw"}},
			wantErr: true,
		},
		{
			name:    "test3",
			args:    args{ipwhitelist: []string{"************ rwz", "************ rw"}},
			wantErr: true,
		},
		{
			name:    "test3",
			args:    args{ipwhitelist: []string{"************      rw", "************ rw"}},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkIPWhiteList(tt.args.ipwhitelist); (err != nil) != tt.wantErr {
				t.Errorf("checkIPWhiteList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 单侧：bns白名单校验
func TestCheckBNSWhiteList(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		action       string
		bnswhitelist []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: missing privilege",
			args: args{
				action: "del",
				bnswhitelist: []string{
					"blind-kj-zk.siod-kafka rw",
					"blind-kj-broker.siod-kafka",
				},
			},
			wantErr: true,
		},
		{
			name: "test2: wrong privilege",
			args: args{
				action: "del",
				bnswhitelist: []string{
					"blind-kj-zk.siod-kafka r",
					"blind-kj-broker.siod-kafka rwss",
				},
			},
			wantErr: true,
		},
		{
			name: "test3: get instances failed",
			args: args{
				action: "add",
				bnswhitelist: []string{
					"blind-kj-broker.siod-daiqi rw",
					"blind-kj-zk.siod-kafka rw",
				},
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {

				httpmock.RegisterResponder("GET", "http://localhost:1793/apptreeNew/v1/products/siod-kafka/apps/blind-kj-broker/instances?pageNo=1&pageSize=200&disable=0",
					// httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/v1/products/siod-kafka/apps/blind-kj-broker/instances?pageNo=1&pageSize=200&disable=0`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "page": {"totalCount":1}, "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []}, 
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}, 
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				action: "add",
				bnswhitelist: []string{
					"blind-kj-broker.siod-kafka rw",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := checkBNSWhiteList(tt.args.action, tt.args.bnswhitelist); (err != nil) != tt.wantErr {
				t.Errorf("checkBNSWhiteList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：更新业务白名单
func TestUpdateProxyWhitelist(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("redis_test")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId     int64
		clusterName string
		action      string
		bnsList     []string
		ipList      []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"page\":{\"totalCount\":0}, \"data\": []}"))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "add",
				bnsList:     []string{"jiayiming.bns rw"},
				ipList:      []string{},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/update"] != 2 {
					t.Errorf("expect call 2 but %d", info["POST =~://[\\w\\W]+/whitelist/update"])
				}
				httpmock.Reset()
			},
		},
		{
			name: "test2",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"page\":{\"totalCount\":0}, \"data\": []}"))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "add",
				bnsList:     []string{"jiayiming.bns"},
				ipList:      []string{},
			},
			wantErr: true,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/whitelist/update"] != 0 {
					t.Errorf("expect call 0 but %d", info["POST =~://[\\w\\W]+/whitelist/update"])
				}
				httpmock.Reset()
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateProxyWhitelist(tt.args.stageId, tt.args.clusterName, tt.args.action, tt.args.bnsList, tt.args.ipList)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateProxyWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
