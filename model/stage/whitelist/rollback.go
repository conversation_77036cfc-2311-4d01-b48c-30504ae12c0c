package whitelist

import (
	"dt-common/ent"
	"dt-common/ent/proxy"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-manager/library/ragent"
)

// 回滚proxy白名单
func rollbackProxy(stageId int64, proxyList []*ent.Proxy) error {
	// 调用proxy agent进行回滚
	for item := range proxyList {
		err := ragent.RollbackWhitelist(proxyList[item].IP, &ragent.RollbackParams{
			Port: proxyList[item].Port,
		})
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "Failed to call redis-agent to rollback proxy whitelist, stageId=%d, proxy=%s:%d, error=(%v)", stageId, proxyList[item].IP, proxyList[item].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to call redis-agent to rollback proxy %s:%d whitelist, stageId=%d", proxyList[item].IP, proxyList[item].Port, stageId)
	}
	omodel.StageAppendInfoLog(stageId, "All proxy whitelists have been rolled back")

	return nil
}

// 回滚redis白名单
func rollbackRedis(stageId int64, redisList []*ent.Redis) error {
	// 调用redis agent进行回滚
	for item := range redisList {
		err := ragent.RollbackWhitelist(redisList[item].IP, &ragent.RollbackParams{
			Port: redisList[item].Port,
		})
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "Failed to call redis-agent to rollback redis whitelist, stageId=%d, redis=%s:%d, error=(%v)", stageId, redisList[item].IP, redisList[item].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to call redis-agent to rollback redis %s:%d whitelist, stageId=%d", redisList[item].IP, redisList[item].Port, stageId)
	}
	omodel.StageAppendInfoLog(stageId, "All rdis whitelists have been rolled back")

	return nil
}

// 回滚sentinel白名单
func rollbackSentinel(stageId int64, sentinelList []*ent.Sentinel) error {
	// 调用sentinel agent进行回滚
	for item := range sentinelList {
		data := ragent.RollbackParams{
			Port: sentinelList[item].Port,
		}
		err := ragent.RollbackWhitelist(sentinelList[item].IP, &data)
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "Failed to call redis-agent to rollback sentinel whitelist, stageId=%d, sentinel=%s:%d, error=(%v)", stageId, sentinelList[item].IP, sentinelList[item].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "Succeed to call redis-agent to rollback sentinel %s:%d whitelist, stageId=%d", sentinelList[item].IP, sentinelList[item].Port, stageId)
	}
	omodel.StageAppendInfoLog(stageId, "All sentinel whitelists have been rolled back")

	return nil
}

// 回滚proxy白名单
func RollbackProxyWhitelist(stageId int64, clusterName string) error {
	// 获取集群proxy列表
	db, err := mysql.Database()
	if err != nil {
		return err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	proxyList, err := db.Proxy.Query().Where(proxy.ClusterName(clusterName), proxy.Docker(omodel.DEPLOY_ENV_BBC)).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "Failed to query proxy instance, error=(%v)", err)
		return errs.CodeDatabase.Detail("查询proxy出错，请稍后刷新重试")
	}
	omodel.StageAppendInfoLog(stageId, "Succeed to query proxy instance, proceed to call redis-agent to rollback whitelist")

	err = rollbackProxy(stageId, proxyList)
	if err != nil {
		return err
	}

	return nil
}

// 回滚各模块白名单
func RollbackManageWhitelist(stageId int64, clusterName string) error {
	proxyList, redisList, sentinelList, err := getInstanceByClusterName(clusterName)
	if err != nil {
		return err
	}

	err = rollbackProxy(stageId, proxyList)
	if err != nil {
		return err
	}

	err = rollbackRedis(stageId, redisList)
	if err != nil {
		return err
	}

	err = rollbackSentinel(stageId, sentinelList)
	if err != nil {
		return err
	}

	return nil
}
