package stage

import (
	"fmt"
	"reflect"
	"regexp"
	"sync"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"redis-manager/env"
)

func TestAsync(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	objC := env.MockCluster("aysnc_test")
	_, objS := env.MockTaskWithStage(objC)

	// mock xweb
	httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-xweb/api/task/stage/callback`),
		httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))

	wg := &sync.WaitGroup{}
	wg.Add(2)
	type schema struct {
		stageId int64
		syncFun interface{}
		args    []reflect.Value
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1",
			args: schema{
				stageId: objS.ID,
				syncFun: func() {},
			},
			wantErr: true,
		},
		{
			name: "test2",
			args: schema{
				stageId: objS.ID,
				syncFun: func() error { return nil },
			},
			wantErr: false,
			expect: func(t *testing.T) {
				time.Sleep(1 * time.Second)
				urlMap := httpmock.GetCallCountInfo()
				if urlMap["POST =~://[\\w\\W]+/redis-xweb/api/task/stage/callback"] != 1 {
					t.Errorf("123")
				}
				wg.Done()
			},
		},
		{
			name: "test3",
			args: schema{
				stageId: objS.ID,
				syncFun: func() error { return fmt.Errorf("mock error") },
			},
			wantErr: false,
			expect: func(t *testing.T) {
				time.Sleep(1 * time.Second)
				urlMap := httpmock.GetCallCountInfo()
				if urlMap["POST =~://[\\w\\W]+/redis-xweb/api/task/stage/callback"] != 2 {
					t.Errorf("123")
				}
				wg.Done()
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := Async(tt.args.stageId, tt.args.syncFun, tt.args.args...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Async() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
	wg.Wait()
}
