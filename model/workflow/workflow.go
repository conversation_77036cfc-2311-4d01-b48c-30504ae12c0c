package workflow

import (
	"fmt"
	"runtime/debug"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-xweb/model/stage/cluster"
	"redis-xweb/model/stage/deploy"
	"redis-xweb/model/stage/migration"
	"redis-xweb/model/stage/process"
	"redis-xweb/model/stage/whitelist"
)

// ===============================
//          阶段执行/取消
// ===============================

// 阶段执行/回滚函数定义
type StageFunc func(*ent.Stage) error

// 阶段类型和执行函数定义
type StageTypeFunc struct {
	Exec     StageFunc
	Rollback StageFunc
	Stop     StageFunc
}

var (
	// 同步任务需要注明Sync前缀，任务结束返回 errs.Success
	syncEmptyStage StageFunc = func(s *ent.Stage) error { return errs.Success }
	stageTypeMap             = map[string]StageTypeFunc{
		// 空阶段
		omodel.STAGE_TYPE_EMPTY: {Exec: syncEmptyStage, Rollback: syncEmptyStage},

		/** 全局预案 */
		omodel.STAGE_TYPE_EMERGENCY_CUT:   {Exec: cluster.EmergencyFlowCut}, // 机房切流（有损）
		omodel.STAGE_TYPE_MONITOR_UPGRADE: {Exec: cluster.MonitorUpgrade},   // 监控升级

		/** 部署 */
		omodel.STAGE_TYPE_DEPLOY:        {Exec: deploy.CreateCluster},                                         // 申请实例
		omodel.STAGE_TYPE_ADD_MONITOR:   {Exec: deploy.SyncInitMonitors, Rollback: deploy.SyncDeleteMonitors}, // 添加监控&报警
		omodel.STAGE_TYPE_CREATE_GROUP:  {Exec: deploy.CreateGroup},                                           // 创建SMART
		omodel.STAGE_TYPE_GENERATE_BILL: {Exec: deploy.CreateBill},                                            // 生成账单

		/** Proxy扩缩容 */
		omodel.STAGE_TYPE_PROXY_SCALING: {Exec: cluster.ProxyScaling},
		omodel.STAGE_TYPE_PROXY_ENABLE:  {Exec: cluster.EnableProxyOneByOne, Rollback: migration.DisableInstances},
		omodel.STAGE_TYPE_PROXY_REMOVE:  {Exec: cluster.RemoveTaintedProxy},

		/** 内存扩缩容 */

		/** 白名单 */
		omodel.STAGE_TYPE_UPDATE_WHITELIST: {Exec: whitelist.Update, Rollback: whitelist.Rollback}, // 白名单管理

		/** 实例级操作 */
		omodel.STAGE_TYPE_FAILOVER: {Exec: cluster.Failover, Rollback: cluster.RollbackFailover, Stop: cluster.StopFailover}, // 主从切换

		/** 迁移阶段 */
		omodel.STAGE_TYPE_MIGRATION_DEPLOY:           {Exec: deploy.CreateCluster},                                                 // -- 容器迁移：单机房部署
		omodel.STAGE_TYPE_COMPLETE_CLUSTER:           {Exec: migration.CompleteCluster},                                            // -- 容器迁移：扩容一半实例使集群完整
		omodel.STAGE_TYPE_UPDATE_GROUP_APPLIST:       {Exec: deploy.UpdateGroupAppList, Rollback: deploy.RollbackGroupAppList},     // -- 容器迁移：bns-group applist扩缩容
		omodel.STAGE_TYPE_SLAVE_OF:                   {Exec: migration.SlaveOf, Stop: migration.StopSlaveOf},                       // -- 容器迁移：建立主从关系
		omodel.STAGE_TYPE_STOP_NON_DOCKER_SLAVE:      {Exec: migration.StopSlaves, Rollback: migration.StartSlaves},                // -- 容器迁移：关停物理从库
		omodel.STAGE_TYPE_STOP_INSTANCES:             {Exec: process.Stop, Rollback: process.Start},                                // -- 容器迁移：关停实例（proxy/sentinel）
		omodel.STAGE_TYPE_START_INSTANCES:            {Exec: process.Start, Rollback: process.Stop},                                // -- 容器迁移：启动实例（proxy/sentinel）
		omodel.STAGE_TYPE_UNBLOCK_DOCKER_PROXY:       {Exec: migration.UnblockProxyOneByOne, Rollback: migration.DisableInstances}, // -- 容器迁移：解屏蔽容器proxy
		omodel.STAGE_TYPE_CLEAN_BBC_SENTINEL_MONITOR: {Exec: deploy.SyncCleanSentinelMonitors},                                     // -- 容器迁移：清理物理sentinel监控报警
		omodel.STAGE_TYPE_SENTINEL_MONITOR:           {Exec: syncEmptyStage},                                                       // -- 容器迁移：sentinel montior管理
		omodel.STAGE_TYPE_UNMOUNT_INSTANCE:           {Exec: migration.UnmountInstance},                                            // -- 容器迁移：卸载&删除实例
		omodel.STAGE_TYPE_TRANSFORM_BILL_HALF:        {Exec: migration.TransformHalfBill},                                          // -- 容器迁移：物理账单缩一半 + 创建容器账单
		omodel.STAGE_TYPE_TRANSFORM_BILL_DOCKER:      {Exec: migration.TransformDockerBill},                                        // -- 容器迁移：物理账单退还 + 容器账单扩一倍
		omodel.STAGE_TYPE_MODE_CHANGE:                {Exec: cluster.ChangeMode, Rollback: cluster.RollbackMode},                   // 更新集群托管模式，做变更前需要关闭托管
		omodel.STAGE_TYPE_UPDATE_SENTINEL_QUORUM:     {Exec: migration.UpdateQuorum},
	}

	// 回调阶段
	stageCallbackMap = map[string]CallbackFunc{
		omodel.STAGE_TYPE_UPDATE_WHITELIST: {Exec: whitelist.CallbackUpdateWhitelistDatabase},
		omodel.STAGE_TYPE_MIGRATION_DEPLOY: {Exec: migration.CallbackAfterDeployCluster},
		omodel.STAGE_TYPE_COMPLETE_CLUSTER: {Exec: migration.CallbackAfterDeployCluster},
		omodel.STAGE_TYPE_PROXY_SCALING:    {Exec: cluster.CallbackProxyScaling}, // 集群状态、成本计费
	}
)

// 后台执行时，panic不会被gin.Recovery捕获
func AsyncRecovery(stageID int64, result *error) {
	if err := recover(); err != nil {
		// 打印堆栈信息
		logger.Error("[Panic Occured] %v", err)
		logger.Error("[Recovery From Panic] %v", string(debug.Stack()))
		*result = fmt.Errorf("[Panic Occured]: %v", err)
	}
}
