# Redis内存扩缩容功能

## 概述

Redis内存扩缩容功能实现了通过流程控制的方式安全地调整Redis集群的内存配置。由于Redis进程运行在Pod中，Pod不支持动态调整内存quota，因此需要通过替换Pod的方式来实现内存扩缩容。

## 实现原理

### 核心流程
1. **从库替换(第一轮)** - 替换第一个机房(hbb)的从库实例
2. **主从切换** - 手动触发，将主库切换到新的实例上
3. **从库替换(第二轮)** - 替换第二个机房(hba)的从库实例

### 设计考虑
- **安全性**: 只对从库进行替换操作，因为从库不接收线上流量
- **用户影响**: 主从切换设置为手动触发，避免自动操作对用户产生影响
- **分阶段执行**: 分两轮替换从库，确保始终有可用的从库实例

## 代码实现

### 文件结构
```
model/autoflow/
├── mem_scaling.go      # 主要实现文件
├── mem_scaling_test.go # 单元测试
└── common.go          # 注册到JobMap
```

### 核心函数

#### MemoryScaling
```go
func MemoryScaling(clusterName string, paramStr string) (*TaskArgs, error)
```

**参数验证**:
- 内存大小必须为正数
- 内存大小必须与当前配置不同
- 内存大小必须能被分片数整除
- 单个分片内存大小必须在1-16GB范围内

**返回结果**:
- 包含3个阶段的TaskArgs结构
- 任务名称根据扩容/缩容自动确定

### 参数结构

#### MemoryScalingSchema
```go
type MemoryScalingSchema struct {
    StorageSize int `json:"storageSize"` // 新的内存大小，单位GB
}
```

## 使用方法

### 1. 通过Flow系统调用
```json
{
    "type": "memoryScaling",
    "clusterName": "your-cluster-name",
    "parameter": "{\"storageSize\": 16}"
}
```

### 2. 直接调用函数
```go
taskArgs, err := MemoryScaling("cluster-name", `{"storageSize": 16}`)
if err != nil {
    // 处理错误
}
// 使用taskArgs创建任务和阶段
```

## 阶段详情

### 阶段1: 从库替换(第一轮)
- **类型**: `STAGE_TYPE_MIGRATION_DEPLOY`
- **自动执行**: 是
- **目标**: hbb机房的从库实例
- **参数**: 包含新的内存大小和分片内存配置

### 阶段2: 主从切换
- **类型**: `STAGE_TYPE_FAILOVER`
- **自动执行**: 否（手动触发）
- **目标**: 将主库切换到新实例
- **参数**: 并发控制和间隔时间配置

### 阶段3: 从库替换(第二轮)
- **类型**: `STAGE_TYPE_MIGRATION_DEPLOY`
- **自动执行**: 是
- **目标**: hba机房的从库实例
- **参数**: 包含新的内存大小和分片内存配置

## 测试

### 运行测试
```bash
cd model/autoflow
go test -v -run TestMemoryScaling
```

### 测试覆盖
- 参数解析错误
- 负数内存大小
- 相同内存大小
- 内存大小不能被分片数整除
- 分片内存大小超出范围
- 成功的内存扩容
- 成功的内存缩容

## 注意事项

1. **主从切换影响**: 主从切换会对用户产生短暂影响，因此设置为手动触发
2. **内存限制**: 单个分片内存大小限制在1-16GB范围内
3. **分片约束**: 总内存大小必须能被分片数整除
4. **机房顺序**: 先替换hbb机房，再替换hba机房的从库

## 后续优化

1. **任务类型**: 可以添加专门的`TASK_TYPE_MEMORY_SCALING`任务类型
2. **阶段类型**: 可以添加专门的`STAGE_TYPE_REDIS_REPLACE`阶段类型
3. **参数扩展**: 可以支持更多的配置参数，如指定机房顺序等
4. **监控集成**: 集成监控和告警，实时跟踪扩缩容进度
