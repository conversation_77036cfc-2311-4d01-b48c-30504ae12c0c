package ragent

import (
	"fmt"

	"dt-common/errs"
)

// Proxy启动
func ProxyStart(ip string, port int) error {
	operateParams := OperateParams{
		Port: port,
	}
	// 发送请求，post超时时间：5s
	subUrl := "/proxy/start"
	resp, err := post(ip, subUrl, &operateParams)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s)", resp.Code, resp.Message)
	}

	return nil
}

// Proxy关停
func ProxyStop(ip string, port int) error {
	operateParams := OperateParams{
		Port: port,
	}
	// 发送请求，post超时时间：5s
	subUrl := "/proxy/stop"
	resp, err := post(ip, subUrl, &operateParams)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s)", resp.Code, resp.Message)
	}

	return nil
}
