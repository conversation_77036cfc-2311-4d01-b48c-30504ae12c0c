package ragent

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

var cfg *Config

// agent配置参数
type Config struct {
	Token   string        `yaml:"token"`   // redis-agent通讯token
	Port    int           `yaml:"port"`    // redis-agent端口
	Timeout time.Duration `yaml:"timeout"` // 超时时间, 默认5秒
}

// 初始化配置
func Init(conf *Config) error {
	if conf.Token == "" {
		return fmt.Errorf("agent token is empty")
	}

	if conf.Timeout == 0 {
		conf.Timeout = 5
	}

	cfg = conf
	return nil
}

// agent返回值结构
type response struct {
	Code    string      `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data"` // 返回结果可能是字符串，也可能是数组
}

// 向agent发送请求
func post(ip string, subUrl string, reqBody interface{}) (*response, error) {
	if subUrl == "" {
		return nil, fmt.Errorf("subURL is empty")
	}
	url := fmt.Sprintf("http://%s:%d%s", ip, cfg.Port, subUrl)

	// request body
	body, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost, url, strings.NewReader(string(body)))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", cfg.Token)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	client := &http.Client{
		Timeout: cfg.Timeout * time.Second,
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	result := response{}
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
