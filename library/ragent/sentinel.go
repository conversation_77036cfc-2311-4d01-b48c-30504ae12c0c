package ragent

import (
	"fmt"

	"dt-common/errs"
)

// Sentinel启动接口
func SentinelStart(ip string, port int) error {
	operateParams := OperateParams{
		Port: port,
	}
	// 发送请求，post超时时间：5s
	subUrl := "/sentinel/start"
	resp, err := post(ip, subUrl, &operateParams)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s)", resp.Code, resp.Message)
	}

	return nil
}

// Sentinel关停接口
func SentinelStop(ip string, port int) error {
	operateParams := OperateParams{
		Port: port,
	}
	// 发送请求，post超时时间：5s
	subUrl := "/sentinel/stop"
	resp, err := post(ip, subUrl, &operateParams)
	if err != nil {
		return fmt.E<PERSON><PERSON>("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s)", resp.Code, resp.Message)
	}

	return nil
}
