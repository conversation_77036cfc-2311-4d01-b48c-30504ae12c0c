package ragent

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

// 单测：redis启动
func TestStartRedis(t *testing.T) {
	Init(&Config{
		Token:   "123",
		Port:    6379,
		Timeout: 1,
	})

	// MOCK HTTP
	httpmock.Activate()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				ip:   "127.0.0.1",
				port: 6379,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := RedisStart(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("Start() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：redis关停
func TestStopRedis(t *testing.T) {
	Init(&Config{
		Token:   "123",
		Port:    6379,
		Timeout: 1,
	})

	// MOCK HTTP
	httpmock.Activate()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				ip:   "127.0.0.1",
				port: 6379,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := RedisStop(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("redisStop() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
