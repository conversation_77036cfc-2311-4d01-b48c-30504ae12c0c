package ragent

import (
	"fmt"

	"dt-common/errs"
)

// 重写白名单接口入参结构
type UpdateParams struct {
	Port    int      `json:"port"`
	BnsList []string `json:"bnsList"`
	IpList  []string `json:"ipList"`
	Action  string   `json:"action"`
}

// 回滚白名单接口入参结构
type RollbackParams struct {
	Port int `json:"port"`
}

// 重写白名单
func UpdateWhitelist(ip string, body *UpdateParams) error {
	if len(body.BnsList) == 0 && len(body.IpList) == 0 {
		return fmt.Errorf("bnsList and ipList can not be empty")
	}
	if body.Action != "add" && body.Action != "del" {
		return fmt.Errorf("action must be add or delete")
	}

	// 发送请求
	subUrl := "/whitelist/update"
	resp, err := post(ip, subUrl, &body)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s)", resp.Code, resp.Message)
	}

	return nil
}

// 回滚白名单
func RollbackWhitelist(ip string, body *RollbackParams) error {
	// 发送请求
	subUrl := "/whitelist/rollback"
	resp, err := post(ip, subUrl, &body)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s)", resp.Code, resp.Message)
	}

	return nil
}
