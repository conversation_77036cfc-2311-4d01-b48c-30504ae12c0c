package ragent

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

// 单测：post
func TestPost(t *testing.T) {
	Init(&Config{Token: "123", Port: 6379, Timeout: 1})

	// MOCK HTTP
	httpmock.Activate()
	httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/testapi`),
		httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))

	type args struct {
		ip      string
		subURL  string
		reqBody interface{}
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {

			},
			args: args{
				ip:     "127.0.0.1",
				subURL: "/testapi",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			_, err := post(tt.args.ip, tt.args.subURL, tt.args.reqBody)
			if (err != nil) != tt.wantErr {
				t.Errorf("Start() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
