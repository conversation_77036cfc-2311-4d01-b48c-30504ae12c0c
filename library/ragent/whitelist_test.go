package ragent

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

func TestUpdateWhitelist(t *testing.T) {
	Init(&Config{Token: "123", Port: 6379, Timeout: 1})

	// MOCK HTTP
	httpmock.Activate()

	type args struct {
		ip   string
		body *UpdateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			args: args{
				ip: "127.0.0.1",
				body: &UpdateParams{
					Port:    8080,
					BnsList: []string{},
					IpList:  []string{},
					Action:  "add",
				},
			},
			wantErr: true,
		},
		{
			name: "test2",
			args: args{
				ip: "127.0.0.1",
				body: &UpdateParams{
					Port:    8080,
					BnsList: []string{"qa.test"},
					Action:  "adbd",
				},
			},
			wantErr: true,
		},
		{
			name: "test3",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				ip: "127.0.0.1",
				body: &UpdateParams{
					Port:    8080,
					BnsList: []string{"qa.test"},
					Action:  "add",
				},
			},
			wantErr: false,
		},
		{
			name: "test4",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00100", "msg": "mock not ok"}`))
			},
			args: args{
				ip: "127.0.0.1",
				body: &UpdateParams{
					Port:    8080,
					BnsList: []string{"qa.test"},
					Action:  "add",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateWhitelist(tt.args.ip, tt.args.body)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestRollback(t *testing.T) {
	Init(&Config{Token: "123", Port: 6379, Timeout: 1})

	// MOCK HTTP
	httpmock.Activate()

	type args struct {
		ip   string
		body *RollbackParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/rollback`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				ip:   "************",
				body: &RollbackParams{Port: 8001},
			},
			wantErr: false,
		},
		{
			name: "test2",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/rollback`),
					httpmock.NewStringResponder(200, `{"code": "00001", "msg": "mock not ok"}`))
			},
			args: args{
				ip:   "************",
				body: &RollbackParams{Port: 8001},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := RollbackWhitelist(tt.args.ip, tt.args.body)
			if (err != nil) != tt.wantErr {
				t.Errorf("RollbackWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
