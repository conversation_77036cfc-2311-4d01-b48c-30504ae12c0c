package main

import (
	"fmt"
	"log"
	"runtime"

	"dt-common/logger"
	"redis-manager/config"
	"redis-manager/env"
	"redis-manager/router"
)

func main() {
	//控制main使用cpu的总数,只用一颗cpu
	runtime.GOMAXPROCS(1)

	env.Init()
	logger.Info("initialization successful, server starting...")

	// 获取app config
	var cfg router.Config
	err := config.Get("application", &cfg)
	if err != nil {
		log.Panicf("fail to init http server, err: %s", err.Error())
	}

	app := router.Init(cfg.Mode, cfg.Token)
	app.Run(fmt.Sprintf(":%d", cfg.Port))
}
