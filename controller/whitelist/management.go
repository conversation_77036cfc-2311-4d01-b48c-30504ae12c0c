package whitelist

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-manager/model/stage"
	"redis-manager/model/stage/whitelist"
)

// 更新管理白名单（proxy、redis、sentinel）
func UpdateManageWhitelist(c *gin.Context) {
	// 参数校验
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	args := make([]reflect.Value, 4)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.ClusterName)
	args[2] = reflect.ValueOf(params.Action)
	args[3] = reflect.ValueOf(params.WhiteListBns)
	err = stage.Async(params.StageId, whitelist.UpdateManageWhitelist, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
