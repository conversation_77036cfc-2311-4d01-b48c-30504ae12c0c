package process

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/omodel"
	"redis-manager/model/stage"
	"redis-manager/model/stage/redis"
)

type RedisParams struct {
	StageId   int64              `json:"stageId"`
	BNS       string             `json:"bns"`
	Instances []*omodel.Instance `json:"instances"`
}

// sentinel实例启动(同步)
func StartSlave(c *gin.Context) {
	// 1 参数解析
	var params RedisParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	if params.Instances == nil || len(params.Instances) <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("instance is empty"))
		return
	}

	args := make([]reflect.Value, 3)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.Instances)
	err = stage.Async(params.StageId, redis.StartSlave, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// Sentinel实例关停(同步)
func StopSlave(c *gin.Context) {
	// 1 参数解析
	var params RedisParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	if params.Instances == nil || len(params.Instances) <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("instance is empty"))
		return
	}

	args := make([]reflect.Value, 3)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.Instances)
	err = stage.Async(params.StageId, redis.StopSlave, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
