package cluster

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-manager/model/stage"
	"redis-manager/model/stage/migration"
)

type UnmountInstanceParams struct {
	StageId int64  `json:"stageId"`
	IDC     string `json:"idc"`
}

// 解挂载单边实例
// 1、从BNS中摘除实例
// 2、从数据库中清除记录
func UnmountInstance(c *gin.Context) {
	var params UnmountInstanceParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	// 入参校验
	if params.IDC == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect parameter idc but got empty value"))
		return
	}

	args := make([]reflect.Value, 2)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.IDC)
	err = stage.Async(params.StageId, migration.InstanceUnmount, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
