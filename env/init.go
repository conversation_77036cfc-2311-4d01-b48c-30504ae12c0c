package env

import (
	"log"

	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/redisc"
	"dt-common/xweb"
	"redis-manager/config"
	"redis-manager/library/errc"
	"redis-manager/library/ragent"
)

func Init(configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("fail to init config, err: %s", err.Error())
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("fail to init logger, err: %s", err.<PERSON>rror())
	}
	logger.Init(&logConfig)

	// 初始化 db
	var dbConfig mysql.Config
	err = config.Get("db", &dbConfig)
	// dbConfig.AutoMigrate = true
	if err != nil {
		log.Panicf("fail to init mysql, err: %s", err.Error())
	}
	mysql.Init(&dbConfig)

	// 初始化 noah
	var noahConfig noah.Config
	config.Get("noah", &noahConfig)
	if err != nil {
		log.Panicf("fail to init noah sdk, err: %s", err.Error())
	}
	noah.Init(&noahConfig)

	// 初始化 redis-agent
	var agentConf ragent.Config
	config.Get("redis_agent", &agentConf)
	if err != nil {
		log.Panicf("fail to init redis agent sdk, err: %s", err.Error())
	}
	ragent.Init(&agentConf)

	// 初始化 redisClient
	var redisConfig redisc.Config
	err = config.Get("redis", &logConfig)
	if err != nil {
		log.Panicf("Failed to init logger, error=(%v)", err)
	}
	redisc.Init(&redisConfig)

	// 初始化 redis-xweb
	var xwebConf xweb.Config
	config.Get("redis_xweb", &xwebConf)
	if err != nil {
		log.Panicf("fail to init redis xweb, err: %s", err.Error())
	}
	xweb.Init(&xwebConf)

	// 自定义错误码
	errc.Init()
}
