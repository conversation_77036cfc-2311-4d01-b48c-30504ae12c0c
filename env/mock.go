package env

import (
	"context"
	"log"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/stage"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/redisc"
	"dt-common/xweb"
	"redis-manager/config"
	"redis-manager/library/errc"
	"redis-manager/library/ragent"
)

func MockResponse() {}

// 单元测试Mock初始化
func Mock(t *testing.T, configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("Failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("Failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// 初始化自定义错误
	errc.Init()

	// MOCK redisClient
	var redisConfig redisc.Config
	err = config.Get("redis", &logConfig)
	if err != nil {
		log.Panicf("Failed to init logger, error=(%v)", err)
	}
	redisConfig.Mock = true
	redisc.Init(&redisConfig)

	// 初始化 redis-xweb
	var xwebConf xweb.Config
	config.Get("redis_xweb", &xwebConf)
	if err != nil {
		log.Panicf("fail to init redis xweb, err: %s", err.Error())
	}
	xweb.Init(&xwebConf)

	// 初始化 redis-agent
	var agentConf ragent.Config
	config.Get("redis_agent", &agentConf)
	if err != nil {
		log.Panicf("fail to init redis agent sdk, err: %s", err.Error())
	}
	ragent.Init(&agentConf)

	// MOCK DB
	mysql.MockInit(t)

	// MOCK HTTP
	httpmock.Activate()
}

// MOCK DB Data Cluster
func MockCluster(name string) *ent.Cluster {
	db, err := mysql.Database()
	if err != nil {
		log.Panicf("Failed to get mock mysql client, error=(%v)", err)
	}

	c, err := db.Cluster.Query().Where(cluster.NameEQ(name)).First(context.Background())
	if err != nil {
		c, err = db.Cluster.Create().
			SetName(name).SetAlias(name).SetType("enterprise").SetDocker(0).
			SetArea("public").SetProductLine("siod-kafka").SetSubsystem("test").
			SetStorageSize(10).SetShardNum(2).SetProxyNum(2).
			SetMaxmemoryPolicy("novication").SetSmart("smart.group." + name + ".siod-redis").SetPort(8001).SetPassword("123").
			SetRedisVersion("4.0").SetProxyVersion("a326").
			SetLevel(1).SetDepartment("系统运维部").SetDepartmentID(1000000037).SetOwner("jiayiming_dxm").
			SetRuliuNo(123456).Save(context.Background())
		if err != nil {
			log.Panicf("Failed to get mock mysql client, error=(%v)", err)
		}
	}

	return c
}

// MOCK DB Data Task with a Stage
func MockTaskWithStage(objC *ent.Cluster) (*ent.Task, *ent.Stage) {
	db, err := mysql.Database()
	if err != nil {
		log.Panicf("Failed to get mock mysql client, error=(%v)", err)
	}

	objT, err := db.Task.Create().
		SetClusterID(objC.ID).SetClusterName(objC.Name).
		SetName("test").SetApplicant("tester").SetType("test").SetDescription("test").
		Save(context.Background())
	if err != nil {
		log.Panicf("Failed to mock task, error=(%v)", err)
	}

	objS := MockStage(objT)

	return objT, objS
}

// MOCK DB Data Stage
func MockStage(objT *ent.Task) *ent.Stage {
	db, err := mysql.Database()
	if err != nil {
		log.Panicf("Failed to get mock mysql client, error=(%v)", err)
	}

	seq, err := db.Stage.Query().Where(stage.TaskID(objT.ID)).Count(context.Background())
	if err != nil {
		log.Panicf("Failed to count stage, error=(%v)", err)
	}

	objS, err := db.Stage.Create().
		SetTask(objT).SetClusterName(objT.ClusterName).
		SetName("test").SetType("test").SetSequence(seq + 1).SetParameter("{}").
		Save(context.Background())
	if err != nil {
		log.Panicf("Failed to mock stage, error=(%v)", err)
	}

	return objS
}
